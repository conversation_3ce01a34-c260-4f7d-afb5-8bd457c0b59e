import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';

/// Dialog widgets for account deletion flow
class AccountDeletionDialogs {
  /// Show confirmation dialog for account deletion
  static Future<bool> showConfirmationDialog(BuildContext context) async {
    if (!context.mounted) return false;

    final bool? result = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            'Delete Your Account',
            style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
          ),
          content: Text(
            'Are you sure you want to permanently delete your account? This action cannot be undone. All your data, including ideabooks, ideas, and notes, will be erased. Please ensure you remain in the app until the process is complete.',
            style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
            side: BorderSide(
              color: NoejiTheme.colorsOf(dialogContext).border,
              width: 1,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: Text(
                'Cancel',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              child: Text(
                'Delete Account',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText.copyWith(
                  color: Colors.red,
                ),
              ),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  /// Show loading dialog during account deletion
  static void showLoadingDialog(BuildContext context) {
    if (!context.mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'Deleting your account... Please do not close the app.',
              style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
          side: BorderSide(
            color: NoejiTheme.colorsOf(dialogContext).border,
            width: 1,
          ),
        ),
      ),
    );
  }

  /// Hide loading dialog
  static void hideLoadingDialog(BuildContext context) {
    if (context.mounted) {
      Navigator.of(context).pop();
    }
  }

  /// Show success message
  static void showSuccessMessage(BuildContext context) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Account deleted successfully',
          style: GoogleFonts.afacad(),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// Show error message
  static void showErrorMessage(BuildContext context, String error) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Error deleting account: $error',
          style: GoogleFonts.afacad(),
        ),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// Show re-authentication required dialog
  static Future<bool> showReauthenticationDialog(BuildContext context, String provider) async {
    if (!context.mounted) return false;

    final String providerName = provider == 'google.com' ? 'Google' : 'Apple';
    
    final bool? result = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            'Re-authentication Required',
            style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
          ),
          content: Text(
            'For security reasons, you need to sign in again with $providerName before deleting your account.',
            style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
            side: BorderSide(
              color: NoejiTheme.colorsOf(dialogContext).border,
              width: 1,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: Text(
                'Cancel',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              child: Text(
                'Sign in with $providerName',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }
}
